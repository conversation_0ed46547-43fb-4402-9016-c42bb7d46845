# 鸾鸟（新运维底座）对接产品打包配置说明

**使用说明**

1.  办公网github上本项目中创建对应产品的分支

2.  修改`product.json`文件，配置产品信息

    *   所有对象只有在`product.json`中配置声明才会打包

3.  准备对应`product.json`文件的程序包，程序包可以放在项目中（不建议，包过大会导致打包耗时过长或失败）

    *   配置支持`http`或者`https`, 打包程序会去配置地址下载

    *   配置支持`file`, 打包程序会去项目目录对应层级查找

    *   未配置`product.json`，则需要项目目录层级结构完全匹配

    *   以上都未配置，则会去公司镜像仓库下载

4.  提交分支到github，自动打包暂时不支持，当前需要通知到运维。

**打包目录结构说明**

```markdown
# 文件冲突时，目录层级最深的文件优先级最高。
├── product.json    # 产品配置文件，必须，优先级最高，文件中未配置时，才会根据目录结构自动打包
├── service
│    ├── app
│    ├── base
│    ├── common
│    └── extend
└── configruation
     └──init
```

**产品信息配置文**

*   file: `product.json`

**配置文件说明**

1.  产品包括字段

    *   `version` 产品版本

    *   `name` 产品名称

    *   `type` 产品类型

    *   `base_img` 基础镜像

        *   `name` 基础镜像名称

        *   `version` 镜像版本

        *   `build` 基础镜像构建信息，需要构建时配置

            *   `pkgurl` 基础镜像构建包地址, 支持`http`、`https`、`file`； 非必要，与`Dockerfile`文件中对应

            *   `dockerfile` 基础镜像构建`Dockerfile`文件地址, 支持`http`、`https`、`file`; 非必要，缺省则去公司镜像仓库拉取对应镜像

            *   `env` 构建时需设置的环境变量, 非必要

    *   `service` 服务配置

        *   `app` 业务服务列表

            *   `name` 服务名称

            *   `cardinality` 服务实例基数，支持`1:N`写法（`1`:实例基数；`N`:最大限制数量，不写表示不限制）

            *   `version` 服务版本

            *   `build` 服务构建信息，非必要，缺省优先去构建`workspace/name/docker/`下查找`dockerfile`及`composefile`文件，若不存在去公司镜像仓库拉取对应镜像

                *   `image` 服务镜像名称，注意：不包括仓库url；非必要，缺省镜像命名规则为`name:version`

                *   `pkgurl` 服务包地址, 支持`http`、`https`、`file`； 非必要，与`Dockerfile`文件中对应

                *   `dockerfile` 服务构建`Dockerfile`文件地址, 支持`http`、`https`、`file`; 非必要，缺省则去公司镜像仓库拉取对应镜像

                *   `composefile` 服务构建时需使用的`docker-compose`文件地址, 支持`http、https、file`；非必要，缺省以默认`docker-compose`文件为准

                *   `env` 构建时需设置的环境变量, 非必要

            *   `component` 服务构建时需使用的组件列表, 非必要

                *   `name` 组件名称

                *   `version` 组件版本

                *   `build` 组件构建信息, 字段服务列表中的`build`字段一致，优先级高于服务列表中的`build`字段

        *   `common` 公共业务服务，字段与`app`服务列表字段一致

        *   `base` 基础服务，字段与`app`服务列表字段一致

        *   `extend` 扩展服务，字段与`app`服务列表字段一致

    *   `configruation` 配置相关

        *   `init` 服务初始化配置，当前支持`mysql(sql文件)`, `postgresql(sql文件)`, `nacos(namespace_id.zip)`, `clickhouse`, `nginx`等基础服务或者公共业务服务

            *   `sname` 初始化服务名称

            *   `url` 初始化文件地址，支持`http`、`https`、`file`，`file`支持文件或者目录; 若为目录则会拷贝目录中所有对应文件，如：`mysql`为`*.sql`文件，`nacos`为`namespace_id.zip`文件

        *   `port` 服务端口映射配置

            *   `sname` 映射服务名称

            *   `protocol` 映射端口协议，支持`tcp`、`udp`, 非必要，缺省为`tcp`

            *   `type` 映射类型，支持`NodePort`、`ClusterIP`

            *   `port` 映射端口, `type` 为`ClusterIP`时必填，clusterIp映射端口，上下游保持一致

            *   `clusterIp` 映射地址，`type` 为`ClusterIP`时必填，与后端服务一对一关系

            *   `nodePort` 映射主机端口，`type` 为`NodePort`时必填

            *   `servicePort` 映射服务端口，`type` 为`NodePort`时必填

```json
// product.json
// example
{
    "version": "V5.0R23C10",
    "name": "AILPHA",
    "type": "GA",
    "base_img": [
        {
            "name": "ailpha-base:v2.0",
            "url": "file://ailpha-base:v2.0.tar.gz"
        }
    ],
    "service": {
        "app": [
            {
                "name": "exemple-app1",
                "cardinality": 1,
                "version": "v3.4.14",
                "build": {
                    "image": "exemple-app1:v3.4.14",
                    "pkgurl": "http://xxxx/exemple-app1.tar.gz",
                    "dockefile": "file://docker/Dockerfile",
                    "composefile": "file://docker/docker-compose.yml"
                }
            }
        ],
        "common": [
            {
                "name": "dasca",
                "version": "v2.2.28_beta_dev",
                "build": {
                    "pkgurl": "http://xxxx/dasca.tar.gz",
                    "composefile": "file://docker/docker-compose.yml"
                },
                "component": [
                    {
                        "name": "auth",
                        "cardinality": 1,
                        "version": "v2.0.5",
                        "build": {
                            "image": "dasca-auth:v2.0.5",
                            "dockefile": "file://docker/Dockerfile"
                        }
                    },
                    {
                        "name": "center",
                        "cardinality": 1,
                        "version": "1.23.4",
                        "build": {
                            "image": "dasca-center:v2.0.5",
                            "dockefile": "file://docker/Dockerfile"
                        }
                    },
                    {
                        "name": "zuul",
                        "cardinality": 1,
                        "version": "v2.0.5",
                        "build": {
                            "image": "dasca-zuul:v2.0.5",
                            "dockefile": "file://docker/Dockerfile"
                        }
                    }
                ]
            }
        ],
        "base": [
            {
                "name": "zookeeper",
                "cardinality": 3,
                "version": "3.8.3",
                "build": {
                    "image": "zookeeper:3.8.3"
                }
            }
        ],
        "extend": []
    },
    "configruation": {
        "init": [
            {
                "sname": "postgres",
                "url": "http://xxxx/service1.tar.gz"
            },
            {
                "sname": "mysql",
                "url": "http://xxxx/service1.tar.gz"
            },
            {
                "sname": "nacos",
                "url": "http://xxxx/service1.tar.gz"
            },
            {
                "sname": "nginx",
                "url": "http://xxxx/service1.tar.gz"
            }
        ],
        "ports": [
            {
                "sname": "bigdata",
                "type": "ClusterIP",
                "vip": "**************",
                "port": 8901
            },
            {
                "sname": "nginx",
                "type": "NodePort",
                "sport": 443,
                "dport": 443
            }
        ]
    }
}
```

**服务目录结构说明**

```markdown
app.name        // 服务目录，对应product.json中service.app[].name, 不区分大小写，即名称大小为同一个服务
├── docker
│   ├── Dockerfile
│   ├── docker-entrypoint.sh
│   └── docker-compose.yml
├── init
│    ├── postgresql
│    │   └── {{app.name}}.sql
│    ├── mysql
│    │   └── {{app.name}}.sql
│    ├── nacos
│    │   └── namespace_id.zip       // namespace_id为nacos的命名空间id，规则为nacosid_{{product.name}}_{{product.type}},如nacosid_ailpha_ga
│    └── nginx
│        └── nginx-config.tar.gz
└── project
```

*注：{{...}}为product.json中对象*

**业务服务编写Dockerfile说明**

```Dockerfile
# example Dockerfile
# 基础镜像部分，按照运行时进行选择
FROM ailpha-registry:5000/ailpha-base:v2.0

# env 环境变量，主要定义需要人为控制的写入的环境变量
ENV TZ=Asia/Shanghai

# port 定义端口
EXPOSE 80/tcp
EXPOSE 443/tcp
EXPOSE 9000/udp

# prepare 准备阶段，主要是定义工作目录，可以根据实际情况调整
WORKDIR /usr/local/Service

# prepare 环境准备阶段，主要包括程序需要的软件包的安装，一般为长期使用变化不大, 
COPY rpmlibs /home/<USER>
ADD  http://project_uri/pkg.tar.gz /usr/local/Service
RUN rpm -ivh /home/<USER>/*.rpm

# init 主要包含初始化数据部分，包括规则包，第三方数据等，变动比较频繁
COPY initRule  /home/<USER>
COPY components /home/<USER>
RUN chmod u+x /home/<USER>/tshark/install.sh
RUN /home/<USER>/tshark/install.sh

# app 主要包含程序主文件部分
COPY AXDR-*  /usr/local/Service/

# runner 启动脚本部分呢，主要定义启动脚本相关参数,根据需要添加初始化启动脚本
COPY docker/docker-entrypoint.sh /usr/local/bin/
RUN chmod u+x /usr/local/bin/docker-entrypoint.sh
ENTRYPOINT [ "/usr/local/bin/docker-entrypoint.sh" ]

# 程序直接启动命令
# 举例中JAVA_OPTS在docker-entrypoint.sh中定义，CMD可以根据需要调整
CMD java $JAVA_OPTS com.dbapp.LogsaasWebApplication

```

**业务服务编写docker-entrypoint.sh说明**

```sh
#!/bin/sh

# 容器环境初始化相关操作，根据需要调整

exec "$@" # 重要：最后一行且不可删除
```

**业务服务编写docker-compose.yml说明**

```yaml
# exemple docker-compose.yml

version: "3"
services:
  #服务名称
  Mirror:
    #定义镜像名称
    image: ailpha-registry:5000/mirror:V2.0R23C10
    #定义容器名称, 主机ID-服务名-实例ID，全量包ID会自动生成
    container_name: 1-Mirror-1
    #引用环境变量文件 .env
    env_file:
      - midware.env
    labels:
      - "cn=1-Mirror-1"
      - "sn=Mirror"
    #定义部署相关
    deploy:
      #重启策略
      restart_policy:
        condition: unless-stopped
      #资源限制
      resources:
        limits:
          memory:  10G
        reservations:
          memory: 100M
    #定义健康检查
    healthcheck:
      test: curl -kfs https://localhost:8901/ || exit 1
      interval: 30s
      timeout: 5s
      retries: 20
    #定义容器hostname，主机ID-服务名-实例ID，全量包ID会自动生成
    hostname: 1-Mirror-1
    #定义磁盘挂载
    volumes:
      #统一日志挂载路径  /data/主机ID-服务名-实例ID/logs   容器内路径各应用自行对应
      - /data/1-Mirror-1/logs:/usr/hdp/*******-37/bigdata/mirror-web-api/logs
      #统一持久化数据挂载路径  /data/主机ID-服务名-实例ID/data   容器内路径各应用自行对应
      - /data/1-Mirror-1/data:/data
      #Das-License文件特殊路径
      - /opt/ailpha-install/license/share_data:/share_data
      #Das-License文件特殊路径
      - /opt/ailpha-install/license/d_machines:/var/lib/d_machines
      #系统时间文件路径
      - /etc/localtime:/etc/localtime:ro
    #定义网络参数，全量包自动生成，无需手动设置
    networks:
      - cilium-net
```

