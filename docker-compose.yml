version: '3.8'

services:
  postgres:
    image: pgvector/pgvector:pg16
    environment:
      - POSTGRES_DB=ppt_narrator
      - POSTGRES_USER=ppt_narrator
      - POSTGRES_PASSWORD=ppt_narrator123
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
      - LC_ALL=C.UTF-8
      - LANG=C.UTF-8
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ppt_narrator -d ppt_narrator"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  redis:
      image: redis:7-alpine
      container_name: ppt-narrator-redis
      command: redis-server --appendonly yes
      volumes:
        - redis_data:/data
  #    ports:
  #      - "6379:6379"
      healthcheck:
        test: ["CMD", "redis-cli", "ping"]
        interval: 30s
        timeout: 10s
        retries: 3
      restart: unless-stopped


  mcp-pipeline-server:
    #build: ./mcp
    image: ppt-narrator-mcp-pipeline-server
    ports:
      - "48080:48088"  # SSE port
      - "48081:48089"  # HTTP port
      - "48082:49088"  # Health check port
    environment:
      # Required: PPT Narrator backend URL
      - PPT_NARRATOR_URL=http://ppt-narrator:8080

      # MCP Server Configuration
      - MCP_PORTS=48088,48089
      - MCP_BASE_URL=http://localhost:48088
      - MCP_TRANSPORT=mixed

      # Server Configuration
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - OUTPUT_DIR=output
      - TEMPLATES_DIR=templates
      - AUTO_CLEANUP_FILES=${AUTO_CLEANUP_FILES:-false}

      # DStaff Platform Integration (Optional)
      - DSTAFF_ENABLED=${DSTAFF_ENABLED:-true}
      - DSTAFF_ENDPOINT_URL=${DSTAFF_ENDPOINT_URL:-https://dstaff.dbappsecurity.com.cn}
      - DSTAFF_USE_OFFICIAL_AUTH=${DSTAFF_USE_OFFICIAL_AUTH:-true}

      # Debug Configuration
      - MCP_DEBUG=${MCP_DEBUG:-false}
    restart: unless-stopped
    depends_on:
      - ppt-narrator
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:49088/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Redis for business API
  
  ppt-narrator:
    build: .
    image: ppt-narrator-ppt-narrator
    ports:
      - "38080:8080"
    environment:
      - PORT=8080
      - DATABASE_URL=*****************************************************/ppt_narrator?sslmode=disable&client_encoding=UTF8
      - UPLOAD_DIR=/app/uploads
      - SCREENSHOT_DIR=/app/screenshots
      - VIDEO_DIR=/app/videos
      - TEMP_DIR=/app/temp
      - AI_PROVIDER=openai
      - OPENAI_API_KEY=sk-iuEJ0PFBn0va5K9T7eA3FaF0D4814b8c8e70AbC9146dDdA3
      - OPENAI_MODEL=gpt-5-chat-latest
      - OPENAI_BASE_URL=https://oneapi.wetolink.com/v1
      - MINIMAX_API_KEY=${MINIMAX_API_KEY:-}
      - MINIMAX_GROUP_ID=${MINIMAX_GROUP_ID:-1951099282912186398}
      - MINIMAX_MODEL=${MINIMAX_MODEL:-MiniMax-Text-01}
      - MINIMAX_BASE_URL=${MINIMAX_BASE_URL:-https://api.minimaxi.com/v1/text/chatcompletion_v2}
      - TTS_PROVIDER=${TTS_PROVIDER:-minimax}
      - TTS_VOICE=${TTS_VOICE:-alloy}
      - TTS_SPEED=${TTS_SPEED:-1.0}
      - MINIMAX_TTS_API_KEY=${MINIMAX_TTS_API_KEY:-}
      - MINIMAX_TTS_GROUP_ID=${MINIMAX_TTS_GROUP_ID:-1951099282912186398}
      - MINIMAX_TTS_MODEL=${MINIMAX_TTS_MODEL:-speech-02-hd}
      - MINIMAX_TTS_VOICE_ID=${MINIMAX_TTS_VOICE_ID:-male-qn-qingse}
      - MINIMAX_TTS_EMOTION=${MINIMAX_TTS_EMOTION:-happy}
      # EasyVoice TTS 配置
      - EASYVOICE_API_URL=${EASYVOICE_API_URL:-https://easyvoice.wetolink.com/api/v1/tts/generate}
      - EASYVOICE_USERNAME=${EASYVOICE_USERNAME:-}
      - EASYVOICE_PASSWORD=${EASYVOICE_PASSWORD:-}
      - EASYVOICE_VOICE=${EASYVOICE_VOICE:-zh-CN-YunxiNeural}
      - EASYVOICE_RATE=${EASYVOICE_RATE:-0%}
      - EASYVOICE_PITCH=${EASYVOICE_PITCH:-0Hz}
      - EASYVOICE_VOLUME=${EASYVOICE_VOLUME:-0%}
      - LIBREOFFICE_PATH=libreoffice
      - FFMPEG_PATH=ffmpeg
      - SYSTEM_PROMPT=${SYSTEM_PROMPT:-}
      - NARRATOR_ROLE=${NARRATOR_ROLE:-资深教授}
      - NARRATOR_STYLE=${NARRATOR_STYLE:-亲切自然}
      - TARGET_AUDIENCE=${TARGET_AUDIENCE:-大学生}
      - SPEAKING_TONE=${SPEAKING_TONE:-轻松友好}
      - SPEECH_NATURALNESS=${SPEECH_NATURALNESS:-高度口语化}
      - MAX_TOKENS=${MAX_TOKENS:-4000}
      - TEMPERATURE=${TEMPERATURE:-0.7}
      # 字幕配置
      - SUBTITLE_ENABLED=${SUBTITLE_ENABLED:-false}
      - SUBTITLE_FONT_SIZE=${SUBTITLE_FONT_SIZE:-24}
      - SUBTITLE_FONT_COLOR=${SUBTITLE_FONT_COLOR:-#FFFFFF}
      - SUBTITLE_BACKGROUND_COLOR=${SUBTITLE_BACKGROUND_COLOR:-}
      - SUBTITLE_POSITION=${SUBTITLE_POSITION:-bottom}
      - SUBTITLE_FONT_FAMILY=${SUBTITLE_FONT_FAMILY:-Arial}
      - SUBTITLE_OUTLINE=${SUBTITLE_OUTLINE:-2}
      - SUBTITLE_SHADOW=${SUBTITLE_SHADOW:-true}
    volumes:
      - ppt_uploads:/app/uploads
      - ppt_screenshots:/app/screenshots
      - ppt_videos:/app/videos
      - ppt_temp:/app/temp
      - ppt_audio:/app/audio
      - ppt_work:/app/work
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s


volumes:
  postgres_data:
  redis_data:
  ppt_uploads:
  ppt_screenshots:
  ppt_videos:
  ppt_temp:
  ppt_audio:
  ppt_work:
  business_logs: